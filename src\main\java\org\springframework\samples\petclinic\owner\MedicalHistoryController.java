/*
 * Copyright 2012-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.samples.petclinic.owner;

import java.util.Collection;
import java.util.Optional;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST Controller for medical history operations
 *
 * <AUTHOR> PetClinic Team
 */
@RestController
@RequestMapping("/api")
public class MedicalHistoryController {

	private final OwnerRepository owners;

	public MedicalHistoryController(OwnerRepository owners) {
		this.owners = owners;
	}

	/**
	 * Get complete medical history for a pet by pet ID
	 * @param petId the ID of the pet
	 * @return ResponseEntity containing the pet's medical history
	 */
	@GetMapping("/pets/{petId}/medical-history")
	public ResponseEntity<MedicalHistoryResponse> getMedicalHistory(@PathVariable int petId) {
		// Find the owner that has this pet
		Optional<Owner> ownerOpt = owners.findAll().stream()
			.filter(owner -> owner.getPets().stream().anyMatch(pet -> pet.getId().equals(petId)))
			.findFirst();
		
		if (ownerOpt.isEmpty()) {
			return ResponseEntity.notFound().build();
		}
		
		Owner owner = ownerOpt.get();
		Pet pet = owner.getPet(petId);
		
		if (pet == null) {
			return ResponseEntity.notFound().build();
		}
		
		Collection<Visit> visits = pet.getVisits();
		MedicalHistoryResponse response = new MedicalHistoryResponse(pet, visits);
		
		return ResponseEntity.ok(response);
	}

	/**
	 * Response class for medical history
	 */
	public static class MedicalHistoryResponse {
		private final Pet pet;
		private final Collection<Visit> visits;

		public MedicalHistoryResponse(Pet pet, Collection<Visit> visits) {
			this.pet = pet;
			this.visits = visits;
		}

		public Pet getPet() {
			return pet;
		}

		public Collection<Visit> getVisits() {
			return visits;
		}
	}
}
