/*
 * Copyright 2012-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.samples.petclinic.medication;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Repository class for <code>Medication</code> domain objects
 *
 * <AUTHOR> PetClinic Team
 */
public interface MedicationRepository extends JpaRepository<Medication, Integer> {

	/**
	 * Retrieve all {@link Medication}s from the data store.
	 * @return a List of {@link Medication}s.
	 */
	@Query("SELECT m FROM Medication m ORDER BY m.name")
	@Transactional(readOnly = true)
	List<Medication> findAll();

}
